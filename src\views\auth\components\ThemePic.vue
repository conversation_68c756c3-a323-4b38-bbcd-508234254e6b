<template>
    <div class="theme-pic">
        <img class="pic" :src="logoImg" alt="pic" />
    </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import ThemePic from '@/assets/images/auth/theme_pic.png'
import fileService from '@/service/fileService'

const store = useStore<RootState>()

const oemInfo = computed(() => {
    const { oemConfig } = store.state.auth || {}
    const { modules } = oemConfig || {}
    const { config } = modules?.[0] || {}
    return config
})

const oemStorage = sessionStorage.getItem('oemInfo')
const logoImg = computed(() => {
    if(oemInfo?.value?.logoImg){
        return fileService.getFileUrl(oemInfo.value.logoImg)
    }else if(oemStorage){
        return fileService.getFileUrl(JSON.parse(oemStorage).logoImg)
    }else{
        return ThemePic
    }
})

onMounted(() => {
    console.log('ThemePic mounted',oemInfo.value)
})
</script>

<style scoped>
.theme-pic {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}

@media screen and (max-width: 3000px) {
    .pic {
        width: 600px;
    }
}

@media screen and (max-width: 1600px) {
    .pic {
        width: 400px;
    }
}

@media screen and (max-width: 1200px) {
    .pic {
        width: 312px;
    }
}

@media screen and (max-width: 992px) {
    .pic {
        width: 256px;
    }
}

@media screen and (max-width: 768px) {
    .pic {
        width: 200px;
    }
}

@media screen and (max-width: 576px) {
    .pic {
        width: 150px;
    }
}
</style>
