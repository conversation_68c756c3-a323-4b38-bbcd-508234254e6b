<template>
    <div class="brand-title">
        <img class="logo" :src="loginLogo" alt="logo" />
    </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import CommonLogo from '@/assets/images/leftlogo.png'
import fileService from '@/service/fileService'

const store = useStore<RootState>()

const oemInfo = computed(() => {
    const { oemConfig } = store.state.auth || {}
    const { modules } = oemConfig || {}
    const { config } = modules?.[0] || {}
    return config
})
const oemStorage = sessionStorage.getItem('oemInfo')
const loginLogo = computed(() => {
    if(oemInfo?.value?.loginLogo){
        return fileService.getFileUrl(oemInfo.value.loginLogo)
    }else if(oemStorage){
        return fileService.getFileUrl(JSON.parse(oemStorage).loginLogo)
    }else{
        return CommonLogo
    }
})

onMounted(() => {
    // console.log('BrandTitle mounted1',oemInfo.value)
    // console.log('BrandTitle mounted2',loginLogo.value)
})
</script>

<style lang="scss" scoped>
.brand-title {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding-top: 100px;
    gap: 16px;
    z-index: 2;
}
.branch-name {
    font-size: var(--text-5xl);
    font-weight: var(--weight-500);
    color: var(--main-blue-);
}

@media screen and (max-width: 3000px) {
    .branch-name {
        font-size: var(--text-4xl);
    }

    .logo {
        height: 50px;
    }

    .brand-title {
        gap: 20px;
    }
}

@media screen and (max-width: 2000px) {
    .branch-name {
        font-size: var(--text-4xl);
    }

    .logo {
        height: 46px;
    }

    .brand-title {
        gap: 16px;
    }
}


@media screen and (max-width: 1600px) {
    .branch-name {
        font-size: var(--text-4xl);
    }

    .logo {
        height: 44px;
    }

    .brand-title {
        gap: 14px;
    }
}

@media screen and (max-width: 1200px) {
    .branch-name {
        font-size: var(--text-2xl);
    }

    .logo {
        height: 42px
    }

    .brand-title {
        gap: 12px;
    }
}

@media screen and (max-width: 992px) {
    .branch-name {
        font-size: var(--text-lg);
    }

    .logo {
        height: 40px
    }

    .brand-title {
        gap: 10px;
    }
}

@media screen and (max-width: 768px) {
    .branch-name {
        font-size: var(--text-sm);
    }

    .logo {
        height: 38px;
    }

    .brand-title {
        gap: 8px;
    }
}

@media screen and (max-width: 576px) {
    .branch-name {
        font-size: var(--text-xs);
    }

    .logo {
        height: 36px;
    }

    .brand-title {
        gap: 6px;
    }
}
</style>
