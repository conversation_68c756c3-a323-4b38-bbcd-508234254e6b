import type { IPaginationResponse, ICommonResponse } from './axios'
import type { IAllRecord } from './record'

export interface XTPT {
    cDate?: string //底部时间
    cName?: string //底部公司名
    companyName?: string //左侧顶部文字
    loginLogo?:string //登录页左上角的logo
    logoImg?: string //登录页图片
    tcp?: string //底部备案号
    logoBk?: string //首页上方logo
    webPageTabTitle?: string //网页页签标签
    helpManualAddress?: string //帮助手册地址
}
export interface JYHY { 
    mpLogo?:string //小程序首页logo
    mpHomeBanner?:string //小程序首页顶部图
    mpHomeSpec?:string //小程序首页搜索框下方图
    easyCollect?:boolean//是否开启精简授权
    pyr:boolean//是否开启票易融
    mpQrCode?:string//业务进件二维码海报图
    qrCodeImg?:string//业务进件分享图
    jinrongEduApplyQrcode?:string// 业务进件二维码信息  
}

export interface bgCover {
    gqbgCover?:string //高企报告封面
    swbgCover?:string //税务报告封面
    fpbgCover?:string //发票报告封面
}

export interface other{
    dashboardTitle?:string //数据大屏标题
    sqxy?:string //授权协议
}

interface OEMUploadModule {
    productType: 0 | 1 | 2 | 3
    config: XTPT | JYHY | bgCover | other
}

export interface OEMUploadItem {
    id?:string
    key?: string //渠道名
    domainKey?:string
    domainPem?:string
    domainProtocol?:string
    domain?: string //域名
    modules: OEMUploadModule[]
}

export interface IOEMListParams extends IAllRecord{
    page: number
    pageSize: number
    oem_key?: string
    cName?:string
}

export interface IOEMListResponseItem {
    id:string
    key: string
    domain: string
    domainKey:string
    domainPem:string
    domainProtocol:string
    platform: XTPT,
    cloudService:JYHY,
    report:bgCover,
    other:other
}

export interface IOEMListResponse extends IPaginationResponse{
    data:IOEMListResponseItem[]
}

export interface OEMDetailParams extends IAllRecord{
    key: string
    productType: 0 | 1 | 2 | 3
}

export interface IOEMDetailResponse extends ICommonResponse {
    data: IOEMConfig
}

interface OEMConfigModule {
    config: XTPT 
    productType: 0 | 1 | 2 | 3
    productTypeStr: string
}
export interface IOEMConfig {
    key: string
    modules: OEMConfigModule[]
}