<template>
    <div style="background-color: #f7f7f7; ">
        <div v-if="isPlatManager" class="b-margin-16" style=" background-color: #fff; padding: 16px">
            <!-- 搜索公共组件 -->
            <searchBox
                searchOptionKey="BENEFIT_MANAMENT_SEARCH_OPTIONS"
                @updateSearchParams="updateSearchParams"
                :customConfig="searchConfig"
            >
            </searchBox>
        </div>
        <div style="background-color: #fff; padding: 16px; box-sizing: border-box">
            <el-table
                ref="tableList"
                :data="tableData"
                style="width: 100%;height: 500px;"
                v-loading="tableLoading"
                show-overflow-tooltip
                empty-text="暂无数据"
                :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
            >
                <el-table-column label="权益名称" prop="name" min-width="150" />
                <el-table-column label="剩余额度" prop="remain" />
                <el-table-column label="已使用额度" prop="used" />
                <el-table-column label="过期额度" prop="expired" />
                <el-table-column label="累计总额度" prop="total" />
                <el-table-column label="即将到期（30天）" prop="expire_soon" min-width="50">
                    <template #default="scope">
                        <span class="pointer" style="color: #1966FF;" @click="showDialog(scope.row)">{{ scope.row.expire_soon }} </span>
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" min-width="100">
                    <template #default="scope">
                        <div class="display-flex gap-16">
                            <div
                                class="pointer"
                                style="color: #1966ff"
                                type="primary"
                                @click="toBenefitList(scope.row)"
                            >
                                权益列表
                            </div>
                            <div
                                class="pointer"
                                style="color: #1966ff"
                                type="primary"
                                @click="openBenefitDrawer(scope.row)"
                            >
                                消费明细
                            </div>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
    <div>
        <BenefitDrawer v-model:visible="benefitDrawerVisible" />
    </div>
    <el-dialog 
        v-model="dialogVisible"
        title='权益列表'
        width="60%"
    >
        <div class="border"></div>
        <el-table
            ref="tableList"
            :data="benefiList"
            style="width: 100%"
            v-loading="benefiListLoading"
            show-overflow-tooltip
            empty-text="暂无数据"
            :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
        >
            <el-table-column label="权益名称" prop="name" min-width="150" />
            <el-table-column label="总额度" prop="total" />
            <el-table-column label="剩余额度" prop="remain" />
            <el-table-column label="状态" prop="status" />
            <el-table-column label="充值时间" prop="start_time" />
            <el-table-column label="到期时间" prop="end_time" />
        </el-table>
    </el-dialog>
</template>

<script lang='ts' setup>
import searchBox from '@/components/common/SearchBox.vue'
import { ref, onMounted, computed } from 'vue'
import systemService from '@/service/systemService'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import type { IServiceOrderResponseItem } from '@/types/order'
import BenefitDrawer from '../components/BenefitDrawer.vue'

const dialogVisible = ref(false)
const benefiList = ref([])
const benefiListLoading = ref<boolean>(false)
const tableLoading = ref<boolean>(false)
const store = useStore<RootState>()
const isPlatManager = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    const { tenantId } = user || {}
    return tenantId === ''
})
type CustomConfig = {
  [key: string]: Array<{
    label: string;
    value: string;
  }>;
}

const searchConfig = ref<CustomConfig>()

const updateSearchParams = () =>{

}

const tableData = [
    {
        id: '1',
        name: '线索联系方式',
        remain: 100,
        used: 100,
        expired: 100,
        total: 300,
        expire_soon:30,
        last_expire_time: '2022-01-01 12:00:00',
        tenant:'测试租户1',
    },
    {
        id: '2',
        name: '高新技术科技企业报告',
        remain: 100,
        used: 100,
        expired: 100,
        total: 300,
        expire_soon:30,
        last_expire_time: '2023-01-01 12:00:00',
        tenant:'测试租户2',
    },
    {
        id: '3',
        name: '企业发展数据综合报告分析',
        remain: 100,
        used: 100,
        expired: 100,
        total: 300,
        expire_soon:30,
        last_expire_time: '2024-01-01 12:00:00',
        tenant:'测试租户3',
    },
    {
        id: '4',
        name:'企业财税经营分析报告',
        remain: 100,
        used: 100,
        expired: 100,
        total: 300,
        expire_soon:30,
        last_expire_time: '2025-01-01 12:00:00',
        tenant:'测试租户4',
    },
    {
        id: '5',
        name:'智能外呼',
        remain: 100,
        used: 100,
        expired: 100,
        total: 300,
        expire_soon:30,
        last_expire_time: '2026-01-01 12:00:00',
        tenant:'测试租户5',
    },
]

const showDialog = (service: IServiceOrderResponseItem) => {
    console.log(service)
    dialogVisible.value = true
}

const toBenefitList = (service: IServiceOrderResponseItem) => {
    console.log(service)
}

const benefitDrawerVisible = ref(false)
const openBenefitDrawer = (service: IServiceOrderResponseItem) => {
    console.log(service)
    benefitDrawerVisible.value = true
}

onMounted(() => {
    if(isPlatManager.value){
        systemService.tenantList().then(response => {
            searchConfig.value = {
                ...searchConfig.value,
                tenantId:response.map(item => ({
                    label:item.name,
                    value:item.id
                }))
            }
        })
    }

})

</script>

<style lang='scss' scoped>
@use '@/styles/element-lead.scss';

</style>