<template>
    <div class="height-100  width-100 " v-if="isLoading">
        <div v-if="!isLock" class="display-flex flex-column height-100">
            <div class="contact-types display-flex flex-wrap">
                <div class="pointer text-nowrap contact-type-item tb-padding-4 lr-padding-8 border-radius-4 font-weight-400 font-16 display-flex top-bottom-center left-right-center"
                    :class="activeType === type.val ? 'back-opacity-2 color-blue' : ''" v-for="type in contactTypes"
                    :key="type.val" @click="activeType = type.val">
                    {{ type.label }}({{ getContactTypeNum(type.val) }})
                </div>
            </div>

            <div class="contact-list-box t-margin-16 flex-1">
                <el-scrollbar>
                    <div class="contact-list-item tb-padding-8 lr-padding-16 t-margin-16 border-radius-4 oh relative"
                        v-for="contact in showConstactList" :key="contact.content">
                        <div v-if="contact.tagType"
                            class="tag-item absolute color-white font-12 lr-padding-10 tb-padding-7 text-center text-nowrap display-flex center"
                            :class="contact.tagType === 1 ? 'back-color-gradinent-green' : 'back-color-gradinent-orange'
                                ">
                            <Icon :icon="contact.tagType === 2 ? 'icon-a-huaban280' : 'icon-a-huaban281'" color="#fff"
                                size="18px" class="r-margin-5" />
                            {{ contact.tagType == 1 ? '关键' : '推荐' }}
                        </div>
                        <div class="font-18 font-weight-600">
                            <span>{{ contact.contact || '-' }}</span>
                            <span v-show="contact.positionContent">({{ contact.positionContent }})</span>
                        </div>
                        <div class="t-margin-16 display-flex top-bottom-center">
                            <Icon :icon="contactTypes.find((item) => {
                                return item.val === contact.type
                            })?.icon || 'icon-a-huaban277'
                                " size="14px" class="r-margin-5" />
                            <div class="r-margin-5">{{ contact.content }}</div>
                            <div class="font-16">{{ contact.numArea }}</div>
                        </div>
                        <div class="t-margin-4">
                            <Icon icon="icon-a-huaban279" size="14px" class="r-margin-5" />
                            {{ contact?.firstSourceName || '-' }}
                        </div>
                        <div v-if="permissionService.isTransferNewLeadPermitted()">
                            <div class="t-margin-4" v-if='companyClueInfo.clueType === 0'>
                                <div class="btn" @click="turnLeadByPhone(contact)">转线索 </div>
                            </div>
                            <div class="t-margin-4 display-flex top-bottom-center" v-else>
                                <el-icon :color='commonColor.mainBlue'>
                                    <CircleCheckFilled />
                                </el-icon> 已转
                            </div>
                        </div>

                    </div>
                </el-scrollbar>
            </div>
        </div>
        <div class="no-pay-box display-flex top-bottom-center left-right-center" v-else>
            <div class="mask-pan display-flex top-bottom-center left-right-center">
                <!-- <div>联系方式数量</div> -->
                <div class="width-100">
                    <div class="t-margin-20" v-show="contactTotal !== null && channelType === 1">
                        <span class="contact-count color-blue">{{ contactTotal }}</span>
                        个
                    </div>
                    <el-button class="view-contact-btn pointer font-14" @click="buyContacts"
                        v-if="permissionService.isContactViewPermitted()"> 点击查看 </el-button>
                </div>

            </div>
        </div>
    </div>
    <div class="all-padding-24 height-100" v-else>
        <el-skeleton :rows="15" animated />
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, inject, computed, defineEmits, watch, getCurrentInstance } from 'vue'
import type { Ref } from 'vue'
import type { ContactItem } from '@/types/company'
import eventBus from '@/utils/eventBus'

import type { GsGetCompanyClueInfoResponse } from '@/types/lead'
import Icon from '@/components/common/Icon.vue'
import aicService from '@/service/aicService'
import orderService from '@/service/orderService'
import crmService from '@/service/crmService'
import permissionService from '@/service/permissionService'

import { ElMessageBox, ElMessage } from 'element-plus'

const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))
const companyName: Ref<string> = inject('companyName', ref(''))


const isLoading = ref(false)

const contactList: Ref<ContactItem[]> = ref([])

const isLock: Ref<boolean> = ref(false)

const contactTotal: Ref<number | null> = ref(null)
const instance = getCurrentInstance()
const commonColor = ref(instance?.appContext.config.globalProperties.$commom.color)

const channelType: Ref<number> = ref(1)


const crmBuyStatus = inject('crmBuyStatus')

watch(() => crmBuyStatus, (nVal) => {
    if (nVal === true) {
        isLock.value = true
    }
})
const companyClueInfo: Ref<GsGetCompanyClueInfoResponse> = ref({} as GsGetCompanyClueInfoResponse)
const gsGetCompanyClueInfo = () => {
    crmService.gsGetCompanyClueInfo({ socialCreditCode: socialCreditCode.value }).then(getCrmUserRes => {
        companyClueInfo.value = getCrmUserRes.data
    })
}


const activeType = ref(0)

const emits = defineEmits(['updateBuyStatus'])

const contactTypes = ref([
    {
        label: '全部',
        val: 0,
    },
    {
        label: '手机',
        icon: 'icon-a-huaban277',
        val: 1,
    },
    {
        label: '固话',
        icon: 'icon-a-huaban275',
        val: 2,
    },
    // {
    //     label: 'qq',
    //     icon: 'icon-a-huaban276',
    //     val: 3,
    // },
    {
        label: '邮箱',
        icon: 'icon-a-huaban278',
        val: 4,
    },
])
const turnLeadByPhone = (contact: ContactItem) => {

    crmService.crmAdd({
        clueType: 2,
        socialCreditCode: socialCreditCode.value,
        companyName: companyName.value,
        contactInfo: contact.content,
        contactType: contact.type,
        source: 1
    }).then(res => {
        if (res.errCode) {
            ElMessage.error(`转线索失败:${res.errMsg}`)
            return
        }

        eventBus.$emit('refreshBuyStatus')
        // emits('updateBuyStatus')
    })
}


// 获取联系方式列表
const getContactList = async () => {
    aicService.gsGetContacts({ socialCreditCode: socialCreditCode.value }).then((res) => {
        contactList.value = res.contacts

        contactTotal.value = res.contactNum || 0

        isLock.value = res.isLock === 1 ? true : false

        channelType.value = res.channelType

        isLoading.value = true
        console.log('getContactList', res)
    })
}

const showConstactList: Ref<ContactItem[]> = computed(() => {
    if (activeType.value === 0) {
        return contactList.value
    } else {
        return contactList.value.filter((item) => item.type === activeType.value)
    }
})

// 获取联系方式类型的数量
const getContactTypeNum = (type: number) => {
    if (type === 0) {
        return contactList.value?.length || 0
    }
    return contactList.value.filter((item) => item.type === type).length
}

onMounted(() => {
    if (socialCreditCode.value) {
        getContactList()
        gsGetCompanyClueInfo()
    }
    eventBus.$on('refreshBuyStatus', () => {
        getContactList()
        gsGetCompanyClueInfo()
    })


})
const buyContacts = () => {
    ElMessageBox.confirm('扣除对应线索权益额度，是否确定使用?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            orderService.orderBuyLegal({
                socialCreditCode: socialCreditCode.value,
                companyName: companyName.value,
                serviceKey: 'xs'
            }).then(res => {
                isLock.value = false

                contactList.value = res.contacts

                contactTotal.value = res.contactNum || 0
                emits('updateBuyStatus', true)
                ElMessage({
                    message: '使用成功',
                    type: 'success',
                })
            })
        })
        .catch(() => { })
}

</script>

<style lang='scss' scoped>
.contact-types {
    .contact-type-item {
        height: 32px;
        box-sizing: border-box;
    }
}

.back-opacity-2 {
    background-color: var(--main-blue-op2-);
}

.contact-list-box {
    height: calc(100% - 100px);

    .contact-list-item {
        background: linear-gradient(to bottom, rgba(250, 252, 255, 1) 0%, rgba(242, 247, 255, 1) 100%);
    }

    .tag-item {
        right: 0px;
        top: 0;
        // width: 58px;
        box-sizing: border-box;
        border-radius: 0 4px 0 8px;
    }
}

.no-pay-box {
    background: url('@/assets/images/contact-lock.png') repeat-y;
    width: 100%;
    height: 100%;
    background-size: contain;
    background-position: top center;
}

.mask-pan {
    margin: 25vh auto;
    width: 85%;
    height: 200px;
    padding: 24px 0;
    text-align: center;
    background: var(--main-white);
    font-size: 14px;
    border: 1px solid var(--border-color);
}

.contact-count {
    font-size: 48px;
}

.view-contact-btn {
    width: 46%;
    margin: 10px auto;
    line-height: 32px;
    background: var(--main-blue-);
    color: var(--main-white);
}

.contact-box {}
</style>