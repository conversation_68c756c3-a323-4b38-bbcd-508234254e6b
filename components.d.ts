/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Account: typeof import('./src/components/layouts/components/header/Account.vue')['default']
    AddSalesDynamics: typeof import('./src/components/crm/salse-dynamics/AddSalesDynamics.vue')['default']
    AI: typeof import('./src/components/layouts/components/header/AI.vue')['default']
    AnimationBtn: typeof import('./src/components/common/AnimationBtn.vue')['default']
    AsideContainer: typeof import('./src/components/layouts/default/AsideContainer.vue')['default']
    AsideMenu: typeof import('./src/components/layouts/default/components/aside/AsideMenu.vue')['default']
    B2BInfo: typeof import('./src/components/form-making/custom-components/B2BInfo.vue')['default']
    Back: typeof import('./src/components/layouts/components/header/Back.vue')['default']
    Cascader: typeof import('./src/components/enterprise/ui/Cascader.vue')['default']
    Checkbox: typeof import('./src/components/enterprise/ui/Checkbox.vue')['default']
    CheckboxBtn: typeof import('./src/components/enterprise/ui/CheckboxBtn.vue')['default']
    CollectInfoBar: typeof import('./src/components/crm/CollectInfoBar.vue')['default']
    CompanyBaseInfo: typeof import('./src/components/search/company-detail/CompanyBaseInfo.vue')['default']
    CompanyDetailDrawer: typeof import('./src/components/search/company-detail-drawer/company-detail-drawer.vue')['default']
    CompanyModelInfo: typeof import('./src/components/search/company-detail/CompanyModelInfo.vue')['default']
    ConditionGroup: typeof import('./src/components/match-rules/ConditionGroup.vue')['default']
    ConditionItem: typeof import('./src/components/match-rules/ConditionItem.vue')['default']
    ContactList: typeof import('./src/components/company/ContactList.vue')['default']
    CreateTask: typeof import('./src/components/auto-dialer/create-task/CreateTask.vue')['default']
    CreateTaskDrawer: typeof import('./src/components/auto-dialer/create-task/CreateTaskDrawer.vue')['default']
    CrmBaseInfo: typeof import('./src/components/crm/CrmBaseInfo.vue')['default']
    CrmBaseInfoBase: typeof import('./src/components/crm/crm-base-info/CrmBaseInfoBase.vue')['default']
    CrmBaseInfoContact: typeof import('./src/components/crm/crm-base-info/CrmBaseInfoContact.vue')['default']
    CrmBaseInfoCustomContact: typeof import('./src/components/crm/crm-base-info/CrmBaseInfoCustomContact.vue')['default']
    CrmBaseInfoLead: typeof import('./src/components/crm/crm-base-info/CrmBaseInfoLead.vue')['default']
    CrmBaseInfoOther: typeof import('./src/components/crm/crm-base-info/CrmBaseInfoOther.vue')['default']
    CrmBaseInfoReport: typeof import('./src/components/crm/crm-base-info/CrmBaseInfoReport.vue')['default']
    CrmBaseInfoSystem: typeof import('./src/components/crm/crm-base-info/CrmBaseInfoSystem.vue')['default']
    CrmBasicScoreDetail: typeof import('./src/components/crm/crm-base-info/CrmBasicScoreDetail.vue')['default']
    CrmCustomerStatus: typeof import('./src/components/crm/CrmCustomerStatus.vue')['default']
    CrmDetailDrawer: typeof import('./src/components/crm/CrmDetailDrawer.vue')['default']
    CrmDetailIndicator: typeof import('./src/components/crm/CrmDetailIndicator.vue')['default']
    CrmInvoiceTaxData: typeof import('./src/components/crm/CrmInvoiceTaxData.vue')['default']
    CrmPolicyMatch: typeof import('./src/components/crm/CrmPolicyMatch.vue')['default']
    CrmPolicyMatchDrawer: typeof import('./src/components/crm/crm-policy-match/CrmPolicyMatchDrawer.vue')['default']
    CrmProductMatch: typeof import('./src/components/crm/CrmProductMatch.vue')['default']
    CrmProductMatchDrawer: typeof import('./src/components/crm/crm-product-match/CrmProductMatchDrawer.vue')['default']
    CrmSalesDynamics: typeof import('./src/components/crm/CrmSalesDynamics.vue')['default']
    CurrentOrg: typeof import('./src/components/layouts/components/header/components/profile/components/profile-panel/CurrentOrg.vue')['default']
    CustomSetting: typeof import('./src/components/common/CustomSetting.vue')['default']
    Dropdown: typeof import('./src/components/enterprise/ui/Dropdown.vue')['default']
    ElAffix: typeof import('element-plus/es')['ElAffix']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElInputTag: typeof import('element-plus/es')['ElInputTag']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    Footer: typeof import('./src/components/layouts/components/header/components/profile/components/profile-panel/Footer.vue')['default']
    HeaderContainer: typeof import('./src/components/layouts/default/HeaderContainer.vue')['default']
    Help: typeof import('./src/components/layouts/components/header/Help.vue')['default']
    HelpMenu: typeof import('./src/components/layouts/components/header/components/help/HelpMenu.vue')['default']
    HighSearchRules: typeof import('./src/components/search/HighSearchRules.vue')['default']
    Icon: typeof import('./src/components/common/Icon.vue')['default']
    IndustryMarket: typeof import('./src/components/search/IndustryMarket.vue')['default']
    LabeledItem: typeof import('./src/components/auto-dialer/create-task/components/LabeledItem.vue')['default']
    LineChart: typeof import('./src/components/echart/LineChart.vue')['default']
    MainContainer: typeof import('./src/components/layouts/default/MainContainer.vue')['default']
    MappedFilter: typeof import('./src/components/enterprise/filters/MappedFilter.vue')['default']
    MatchCompany: typeof import('./src/components/match-rules/MatchCompany.vue')['default']
    MatchRules: typeof import('./src/components/match-rules/index.vue')['default']
    Menu: typeof import('./src/components/help/Menu.vue')['default']
    Menus: typeof import('./src/components/layouts/default/components/aside/menu/Menus.vue')['default']
    MessageBox: typeof import('./src/components/layouts/components/header/MessageBox.vue')['default']
    MultiProvinceFilter: typeof import('./src/components/enterprise/filters/MultiProvinceFilter.vue')['default']
    MultiSelectFilter: typeof import('./src/components/enterprise/filters/MultiSelectFilter.vue')['default']
    OpenPage: typeof import('./src/components/auto-dialer/open-page/OpenPage.vue')['default']
    OptionsFilter: typeof import('./src/components/enterprise/filters/OptionsFilter.vue')['default']
    OrgCard: typeof import('./src/components/org/OrgCard.vue')['default']
    OrgList: typeof import('./src/components/layouts/components/header/components/profile/OrgList.vue')['default']
    OrgProfile: typeof import('./src/components/layouts/components/header/components/profile/components/org/OrgProfile.vue')['default']
    PanelMenu: typeof import('./src/components/layouts/components/header/components/profile/components/profile-panel/PanelMenu.vue')['default']
    PieChart: typeof import('./src/components/echart/PieChart.vue')['default']
    Profile: typeof import('./src/components/layouts/components/header/components/profile/components/profile-panel/Profile.vue')['default']
    ProfileGroup: typeof import('./src/components/layouts/components/header/components/profile/ProfileGroup.vue')['default']
    ProfilePanel: typeof import('./src/components/layouts/components/header/components/profile/ProfilePanel.vue')['default']
    RegionCascadeSelect: typeof import('./src/components/enterprise/region/RegionCascadeSelect.vue')['default']
    RegionFilter: typeof import('./src/components/enterprise/filters/RegionFilter.vue')['default']
    RegionMultipleSelect: typeof import('./src/components/enterprise/region/RegionMultipleSelect.vue')['default']
    RegionSelector: typeof import('./src/components/enterprise/region/RegionSelector.vue')['default']
    RelateDialog: typeof import('./src/components/form-making/custom-components/RelateDialog.vue')['default']
    RelateTableColumn: typeof import('./src/components/form-making/custom-components/RelateTableColumn.vue')['default']
    RouterBreadcrumb: typeof import('./src/components/common/RouterBreadcrumb.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchBox: typeof import('./src/components/common/SearchBox.vue')['default']
    SearchCompanyDetail: typeof import('./src/components/search/company-detail/SearchCompanyDetail.vue')['default']
    SearchCompanyFmGenerateForm: typeof import('./src/components/form-making/SearchCompanyFmGenerateForm.vue')['default']
    SearchCompanyFmGenerateMerge: typeof import('./src/components/form-making/SearchCompanyFmGenerateMerge.vue')['default']
    SearchCompanyFmGenerateTable: typeof import('./src/components/form-making/SearchCompanyFmGenerateTable.vue')['default']
    Section: typeof import('./src/components/auto-dialer/create-task/components/Section.vue')['default']
    SectionContent: typeof import('./src/components/auto-dialer/create-task/components/SectionContent.vue')['default']
    SelectFilter: typeof import('./src/components/enterprise/filters/SelectFilter.vue')['default']
    SettingAsideContainer: typeof import('./src/components/layouts/setting/SettingAsideContainer.vue')['default']
    SettingAsideMenu: typeof import('./src/components/layouts/setting/components/aside/SettingAsideMenu.vue')['default']
    SettingHeaderContainer: typeof import('./src/components/layouts/setting/SettingHeaderContainer.vue')['default']
    SettingMainContainer: typeof import('./src/components/layouts/setting/SettingMainContainer.vue')['default']
    SettingMenus: typeof import('./src/components/layouts/setting/components/aside/menu/SettingMenus.vue')['default']
    TagAdd: typeof import('./src/components/tag/TagAdd.vue')['default']
    TagBind: typeof import('./src/components/tag/TagBind.vue')['default']
    TagPopover: typeof import('./src/components/tag/TagPopover.vue')['default']
    Tags: typeof import('./src/components/layouts/components/header/components/profile/components/profile-panel/Tags.vue')['default']
    TaskManagement: typeof import('./src/components/layouts/components/header/TaskManagement.vue')['default']
    TimeRange: typeof import('./src/components/auto-dialer/create-task/components/TimeRange.vue')['default']
    TitlePlaceholder: typeof import('./src/components/common/TitlePlaceholder.vue')['default']
    TransferCrmDialog: typeof import('./src/components/transfer-crm-dialog/TransferCrmDialog.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
