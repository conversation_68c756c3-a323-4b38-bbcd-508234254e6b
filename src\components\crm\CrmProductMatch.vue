<script lang="ts" setup>
import { ref, onMounted, defineEmits } from 'vue'
import type { Ref } from 'vue'
import CrmProductMatchDrawer from '@/components/crm/crm-product-match/CrmProductMatchDrawer.vue'
import crmService from '@/service/crmService'
import type { IGoodsProductItem } from '@/types/lead'

import productBank from '@/assets/images/product-bank.png'

const props = defineProps({
    companyId: {
        type: String,
        required: true
    }
})

const emits = defineEmits(['toReportList'])
const dataList = ref<IGoodsProductItem[]>([])
const getList = () => {
    crmService.goodsFinanceEntMatchRule({ companyId: props.companyId }).then((res) => {
        console.log('获取产品匹配列表结果', res)
        dataList.value = res.map((item: IGoodsProductItem) => { return { ...item, ...item.matchScore, name: item.name } })
    })
}
const checkedProduceId = ref('')
onMounted(() => {
    getList()
})
const handleInvoiceCollect = () => {
    emits('toReportList')
}

const productDetail: Ref<IGoodsProductItem> = ref({} as IGoodsProductItem)
const handleOpenDetail = async (item: IGoodsProductItem) => {
    // checkedProduceId.value = item.id
    // let detailRes = await crmService.goodsFinanceMatchDetail({
    //     companyId: props.companyId,
    //     id: item.id
    // })
    productDetail.value = item
    drawerVisible.value = true
}
const drawerVisible = ref(false)
</script>
<template>
    <div class="display-flex b-margin-16" style="min-width: 898px">
        <Icon class="r-margin-5" icon="icon-a-1tongyong_2Icontubiao_Fill_Check-Circle-Fill" :size="16"
            color="var(--main-green-)" />
        <div class="tips">
            购买报告并
            <span class="collect" @click="handleInvoiceCollect()">进行票税采集</span> 获取更精准的政策和金融匹配信息！
        </div>
    </div>
    <div class="display-flex flex-wrap" style="gap:1%">
        <div class="border-radius-8 border-tag tb-padding-12 lr-padding-16 b-margin-16 pointer" style="width: 48%;"
            v-for="(item, index) in dataList" :key="index" @click="handleOpenDetail(item)">
            <div class="display-flex b-margin-8">
                <div class="r-margin-8">
                    <div class="w-50 h-50 border-radius-8 border-tag">
                        <img :src="item.bannerImags[0]?.url || productBank" alt="" class="width-100"></img>
                    </div>
                </div>
                <div class="flex-1">
                    <div class="display-flex space-between top-bottom-center b-margin-8">
                        <div class="font-16 maxw-250 text-overflow">{{ item.name }}</div>
                        <div class="font-14 color-primary pointer">匹配度：{{ item.matchScore.totalScore }}%</div>
                    </div>
                    <div class="display-flex font-14 b-margin-8">
                        <div class="r-margin-40">最高金额：<span class="color-red">{{ item.spu.moneyLimits }}</span></div>
                        <div>年化率：{{ item.spu.rateDown }}-{{ item.spu.rateUpper }}%</div>
                    </div>
                    <div class="font-14 b-margin-8">贷款期限：{{ item.spu.loanCycle }}</div>
                    <div class="maxw-350 tb-padding-4 lr-padding-8 font-12 text-overflow product-border !color-blue"
                        v-if="item?.sellPoint">
                        {{ item?.sellPoint }}
                    </div>
                </div>
            </div>
            <div class="tb-padding-8 lr-padding-12 product-border display-flex space-around"
                v-if="item.showList?.length">
                <div class="display-flex w-120 top-bottom-center" v-for="(sitem, i) in item.showList" :key="i"
                    v-show="i <= 2">
                    <Icon class="r-margin-5" icon="icon-a-1tongyong_2Icontubiao_Fill_Check-Circle-Fill" :size="16"
                        color="var(--main-green-)" />
                    <div class="font-12">{{ sitem.propLabel }}</div>
                </div>
            </div>
        </div>
    </div>

    <CrmProductMatchDrawer v-model:visible="drawerVisible" :productId="checkedProduceId" :detailInfo="productDetail">
    </CrmProductMatchDrawer>
</template>
<style scoped lang="scss">
@use '@/styles/element-lead.scss';

.tips {
    font-size: 14px;
    color: var(--main-black);

    .collect {
        color: var(--main-blue-);
        cursor: pointer;
    }
}

.text-overflow {
    overflow: hidden;
    text-overflow: ellipsis; // 当内容超出容器时，显示为省略号（...）
    white-space: nowrap;
}

.product-border {
    border: 1px solid rgba(25, 102, 255, 0.2);
    border-radius: 2px;
    background: rgba(25, 102, 255, 0.06);
}
</style>
