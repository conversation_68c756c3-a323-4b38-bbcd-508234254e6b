<template>
    <div class="company-detail-box width-100 display-flex height-100">
        <div class="company-detail flex-1 height-100 display-flex flex-column">
            <CompanyBaseInfo class="base-info-box" ref="baseInfo" @updateCompanyInfo="updateCompanyInfo" />
            <div class="flex-grow-1">
                <CompanyModelInfo ref="modelList" :socialCreditCode="socialCreditCode"/>
            </div>
        </div>
        <div class="contact-box oh lr-padding-24 tb-padding-12 back-color-white height-100">
            <ContactList ref="contactList" />
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, onMounted, onUnmounted, provide, inject } from 'vue'
import type { Ref } from 'vue'

import type { CompanyBaseInfo as CompanyInfoType } from '@/types/company'
import type { GsGetCompanyClueInfoResponse } from '@/types/lead'

import eventBus from '@/utils/eventBus'
import crmService from '@/service/crmService'
import orderService from '@/service/orderService'
import ContactList from '@/components/company/ContactList.vue'
import CompanyBaseInfo from './CompanyBaseInfo.vue'
import CompanyModelInfo from './CompanyModelInfo.vue'

const companyInfo = ref({} as CompanyInfoType)

const companyName: Ref<string> = ref('')

const buyStatus: Ref<boolean> = ref(false)

const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))

const companyClueInfo: Ref<GsGetCompanyClueInfoResponse> = ref({} as GsGetCompanyClueInfoResponse)
provide('companyInfo', companyInfo)
provide('companyName', companyName)
provide('companyClueInfo', companyClueInfo)
provide('buyStatus', buyStatus)

const contactList = ref<InstanceType<typeof ContactList> | null>(null)

const modelList = ref<InstanceType<typeof CompanyModelInfo> | null>(null)
const updateCompanyInfo = (data: CompanyInfoType) => {
    companyInfo.value = data
    companyName.value = data.companyName

    gsGetCompanyBuyStatusInfo()
}

const gsGetCompanyClueInfo = () => {
    crmService.gsGetCompanyClueInfo({ socialCreditCode: socialCreditCode.value }).then(getCrmUserRes => {
        companyClueInfo.value = getCrmUserRes.data
    })
}
const gsGetCompanyBuyStatusInfo = () => {
    orderService.orderCheckEntBuy({
        socialCreditCode: socialCreditCode.value,
        companyName: companyName.value,
        serviceKey: 'xs'
    }).then(getCompanyBuyStatusInfo => {

        if (getCompanyBuyStatusInfo.status === '1') {
            //已购买
            buyStatus.value = true
        }

        console.log(getCompanyBuyStatusInfo)
    })
}

onMounted(() => {
    gsGetCompanyClueInfo()
    eventBus.$on('refreshBuyStatus', () => {
        gsGetCompanyBuyStatusInfo()
        gsGetCompanyClueInfo()
    })
})
onUnmounted(() => {
    eventBus.$off('refreshBuyStatus', () => { })
})
</script>

<style lang='scss' scoped>
.company-detail-box {
    .company-detail {
        background: linear-gradient(to right,
                rgba(235, 241, 255, 1) 0%,
                rgba(255, 255, 255, 1) 70.15%,
                rgba(255, 255, 255, 1) 100%);
        border-right: 1px solid var(--border-color);
        max-width: calc(100% - 338px)
    }

    .contact-box {
        max-width: 438px;
        min-width: 338px;
        width: 22%;
        height: 100%;
    }
}
</style>