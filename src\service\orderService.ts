import http from '@/axios'

import type {
    IServiceOrderPageParams,
    IServiceOrderResponse,
    IOrderServiceStatisticsParams,
    IOrderBuyLegalResponse,
    IOrderParams,
    IOrderCheckEntBuyResponse,
    IServiceOrderResponseItem,
    IOrderCheckEntBuyParams,
    IOrderCheckEntBuyResponseArr,
    IOrderUsageRecordParams,
    IOrderUsageRecordResponse,
    IOrderTransListParams,
    IOrderTransListResponse,
} from '@/types/order'

export default {
    orderServiceStatistics(data: IOrderServiceStatisticsParams): Promise<string[]> {
        return http.get(`/api/zhenqi-order/order/service-statistics`, {
            params: data,
        })
    },
    orderServiceOrderPage(data: IServiceOrderPageParams): Promise<IServiceOrderResponse> {
        return http.get(`/api/zhenqi-order/order/service-order-page`, {
            params: data,
            hideError: true,
        })
    },
    // 查询使用记录
    orderServiceUsagePage(data: IOrderUsageRecordParams): Promise<IOrderUsageRecordResponse> {
        return http.get(`/api/zhenqi-order/order/service-usage-page`, {
            params: data,
            hideError: true,
        })
    },
    orderBuyLegal(data: IOrderParams): Promise<IOrderBuyLegalResponse> {
        return http.post(`/api/zhenqi-order/order/buy-legal`, data)
    },
    orderCheckEntBuy(data: IOrderParams | IOrderCheckEntBuyParams): Promise<IOrderCheckEntBuyResponse | IOrderCheckEntBuyResponseArr> {
        return http.get(`/api/zhenqi-order/order/check-ent-buy`, {
            params: data,
        })
    },
    serviceOrderPage(data: IServiceOrderPageParams): Promise<IServiceOrderResponseItem[]> {
        return http.get(`/api/zhenqi-order/order/service-order-page`, {
            params: data,
        })
    },
    // 订单列表
    orderTransList(data:IOrderTransListParams):Promise<IOrderTransListResponse>{
        return http.get('/api/zhenqi-order/order/trans-list',{
            params:data,
            hideError:true
        })
    }
}
