<template>
    <div class="all-padding-16 width-100 model-info-box" :style="{ 'height': modelHeight + 'px' }" v-if="isLoading">
        <el-tabs v-model="activeCategory" stretch :tab-position="'left'" class="height-100">
            <el-tab-pane class="height-100" :label="category.title" :name="category.name"
                         v-for="category in categoryDetailList" :key="category.name">

                <div v-if="activeCategory === category.name" class="height-100">
                    <el-scrollbar>
                        <div class="display-flex gap-16 flex-wrap model-box tb-padding-10">
                            <div class="model-label text-nowrap  border all-padding-8 border-radius-4 font-12  display-flex top-bottom-center left-right-center"
                                 v-for="item in category.children" :key="item.name" @click="jumpHref(item.name)"
                                 :class="item.showType === 'list' ? (item.total === 0 ? 'not-allow-back not-allow' : 'pointer back-color-white ') : 'pointer back-color-white '">
                                {{ item.title }} <span class="l-margin-5" v-show="item.total && item.total > 0">{{
                                    item.total }}</span>
                            </div>
                        </div>
                        <div v-for="item in category.children" :key="item.name" :id="'model-' + item.name"
                             class="model-item t-margin-21">
                            <div class="model-title b-margin-17">{{ item.title }}</div>
                            <div v-if="item.showType === 'table'">
                                <SearchCompanyFmForm @updateBuyStatus="updateBuyStatus" :modelItem="item"
                                                     :companyRelateItem="relationModel" />
                            </div>
                            <div v-if="item.showType === 'list'">
                                <SearchCompanyFmTable @updateBuyStatus="updateBuyStatus" :modelItem="item"
                                                      :companyRelateItem="relationModel" @updateTotal="
                                                          (total) => {
                                                              updateModelTotal(total, item)
                                                          }
                                                      " />
                            </div>
                            <div v-if="item.showType === 'merge'">
                                <SearchCompanyFmMerge @updateBuyStatus="updateBuyStatus" :modelItem="item" />
                            </div>
                            <div v-if="item.showType === 'custom'">
                                <div v-if="item.name === 'EntRisk'">
                                    <FxgkTable :allTableData="allTableData" :riskTypeList="riskTypeList"></FxgkTable>
                                </div>
                                <div v-if="item.name === 'FXLXBHQST'">
                                    <Fxlxbhqst :allTableData="allTableData" :riskTypeList="riskTypeList" :pieLoading="pieLoading" />
                                </div>
                                <div v-if="item.name === 'FXLXFBT'">
                                    <Fxlxfbt :allTableData="allTableData" :riskTypeList="riskTypeList" :pieLoading="pieLoading" ></Fxlxfbt>
                                </div>
                            </div>
                        </div>
                    </el-scrollbar>
                </div>

            </el-tab-pane>
        </el-tabs>
    </div>
    <div v-else>
        <el-skeleton :rows="5" animated />
    </div>
</template>

<script lang='ts' setup>
import { ref, onMounted, defineExpose, computed, onUnmounted } from 'vue'
import type { Ref } from 'vue'
import type { IGetModelCategoryResponse } from '@/types/company'

import aicService from '@/service/aicService'
import eventBus from '@/utils/eventBus'
import SearchCompanyFmForm from '@/components/form-making/SearchCompanyFmGenerateForm.vue'
import SearchCompanyFmTable from '@/components/form-making/SearchCompanyFmGenerateTable.vue'
import SearchCompanyFmMerge from '@/components/form-making/SearchCompanyFmGenerateMerge.vue'
import indicatorService from '@/service/indicatorService'
import type { IGetRiskTypeData, IRiskEntListItem } from '@/types/indicator'
import FxgkTable from '@/views/risk-management/risk-alerts/components/FxgkTable.vue'
import Fxlxbhqst from '@/views/risk-management/risk-alerts/components/Fxlxbhqst.vue'
import Fxlxfbt from '@/views/risk-management/risk-alerts/components/Fxlxfbt.vue'

const props = defineProps<{
    socialCreditCode: string
}>()

const isLoading: Ref<boolean> = ref(false)

const activeCategory: Ref<string> = ref('')

const categoryDetailList: Ref<IGetModelCategoryResponse[]> = ref([])

const updateModelTotal = (total: number, item: IGetModelCategoryResponse) => {
    item.total = total
}
const jumpHref = (name: string) => {
    // location.hash = `#model-${name}`
    document.getElementById(`model-${name}`)?.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
    })
}

const relationModel: Ref<IGetModelCategoryResponse> = ref({} as IGetModelCategoryResponse)
const getRelateModel = () => {
    aicService.gsGetGetAppointDetailModel({ name: 'CompanyRelation' }).then(res => {
        relationModel.value = res.data
    })
}

const getModelList = () => {
    isLoading.value = false
    aicService.conditionGetDetailModel().then((res: IGetModelCategoryResponse[]) => {
        categoryDetailList.value = res
        if (!activeCategory.value) {
            activeCategory.value = res[0].name
        }
        isLoading.value = true
    })
}

const updateBuyStatus = () => {
    getModelList()
    eventBus.$emit('refreshBuyStatus')
}

const modelHeight = computed(() => {
    let allHeight = document.getElementsByClassName('company-detail')[0].clientHeight
    let baseHeight = document.getElementsByClassName('base-info-box')[0].clientHeight
    console.log('allHeight', allHeight - baseHeight)
    return allHeight - baseHeight
})

const pieLoading=ref(false)
const riskTypeList = ref<IGetRiskTypeData[]>([])
const allTableData = ref<IRiskEntListItem[]>([])
const getRiskEntList = async () => {
    await indicatorService
        .getRiskEntList({
            socialCreditCode: props.socialCreditCode
        })
        .then((res) => {
            console.log('企业风险列表', res)
            const { success, data } = res
            if (success) {
                allTableData.value = data
            }
        })
}

const getRiskTypeData = () => {
    pieLoading.value=true
    indicatorService.getRiskTypeData({
        socialCreditCode:props.socialCreditCode 
    }).then(res => {
        riskTypeList.value = Object.entries(res).map(([name, value]) => ({
            name,
            value
        }))
    }).finally(() => {
        pieLoading.value= false
    })
}

onUnmounted(() => {
    eventBus.$off('refreshBuyStatus', () => { })
})

onMounted(() => {
    getRiskEntList()
    getRiskTypeData()
    getRelateModel()
    getModelList()
    eventBus.$on('refreshBuyStatus', () => {
        getModelList()
    })
})
defineExpose({
    getModelList
})
</script>

<style lang='scss' scoped>
.model-label {
    width: 135px;
}

.model-item {
    .model-title {
        border-left: 4px solid var(--main-blue-);
        padding-left: 8px;
    }
}

.model-info-box {}

.model-box {
    background: linear-gradient(to right,
            rgba(235, 241, 255, 1) 0%,
            rgba(255, 255, 255, 1) 70.15%,
            rgba(255, 255, 255, 1) 100%);
}

.not-allow-back {
    background-color: var(--second-blue);
}
</style>